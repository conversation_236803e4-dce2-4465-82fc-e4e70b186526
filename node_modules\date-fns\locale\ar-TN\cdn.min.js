var W=function(H){return W=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(A){return typeof A}:function(A){return A&&typeof Symbol=="function"&&A.constructor===Symbol&&A!==Symbol.prototype?"symbol":typeof A},W(H)},G=function(H,A){var T=Object.keys(H);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(H);A&&(Z=Z.filter(function(N){return Object.getOwnPropertyDescriptor(H,N).enumerable})),T.push.apply(T,Z)}return T},K=function(H){for(var A=1;A<arguments.length;A++){var T=arguments[A]!=null?arguments[A]:{};A%2?G(Object(T),!0).forEach(function(Z){B0(H,Z,T[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(H,Object.getOwnPropertyDescriptors(T)):G(Object(T)).forEach(function(Z){Object.defineProperty(H,Z,Object.getOwnPropertyDescriptor(T,Z))})}return H},B0=function(H,A,T){if(A=C0(A),A in H)Object.defineProperty(H,A,{value:T,enumerable:!0,configurable:!0,writable:!0});else H[A]=T;return H},C0=function(H){var A=U0(H,"string");return W(A)=="symbol"?A:String(A)},U0=function(H,A){if(W(H)!="object"||!H)return H;var T=H[Symbol.toPrimitive];if(T!==void 0){var Z=T.call(H,A||"default");if(W(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(A==="string"?String:Number)(H)};(function(H){var A=Object.defineProperty,T=function B(U,C){for(var Y in C)A(U,Y,{get:C[Y],enumerable:!0,configurable:!0,set:function X(J){return C[Y]=function(){return J}}})},Z={lessThanXSeconds:{one:"\u0623\u0642\u0644 \u0645\u0646 \u062B\u0627\u0646\u064A\u0629",two:"\u0623\u0642\u0644 \u0645\u0646 \u0632\u0648\u0632 \u062B\u0648\u0627\u0646\u064A",threeToTen:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062B\u0648\u0627\u0646\u064A",other:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062B\u0627\u0646\u064A\u0629"},xSeconds:{one:"\u062B\u0627\u0646\u064A\u0629",two:"\u0632\u0648\u0632 \u062B\u0648\u0627\u0646\u064A",threeToTen:"{{count}} \u062B\u0648\u0627\u0646\u064A",other:"{{count}} \u062B\u0627\u0646\u064A\u0629"},halfAMinute:"\u0646\u0635 \u062F\u0642\u064A\u0642\u0629",lessThanXMinutes:{one:"\u0623\u0642\u0644 \u0645\u0646 \u062F\u0642\u064A\u0642\u0629",two:"\u0623\u0642\u0644 \u0645\u0646 \u062F\u0642\u064A\u0642\u062A\u064A\u0646",threeToTen:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062F\u0642\u0627\u064A\u0642",other:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062F\u0642\u064A\u0642\u0629"},xMinutes:{one:"\u062F\u0642\u064A\u0642\u0629",two:"\u062F\u0642\u064A\u0642\u062A\u064A\u0646",threeToTen:"{{count}} \u062F\u0642\u0627\u064A\u0642",other:"{{count}} \u062F\u0642\u064A\u0642\u0629"},aboutXHours:{one:"\u0633\u0627\u0639\u0629 \u062A\u0642\u0631\u064A\u0628",two:"\u0633\u0627\u0639\u062A\u064A\u0646 \u062A\u0642\u0631\u064A\u0628",threeToTen:"{{count}} \u0633\u0648\u0627\u064A\u0639 \u062A\u0642\u0631\u064A\u0628",other:"{{count}} \u0633\u0627\u0639\u0629 \u062A\u0642\u0631\u064A\u0628"},xHours:{one:"\u0633\u0627\u0639\u0629",two:"\u0633\u0627\u0639\u062A\u064A\u0646",threeToTen:"{{count}} \u0633\u0648\u0627\u064A\u0639",other:"{{count}} \u0633\u0627\u0639\u0629"},xDays:{one:"\u0646\u0647\u0627\u0631",two:"\u0646\u0647\u0627\u0631\u064A\u0646",threeToTen:"{{count}} \u0623\u064A\u0627\u0645",other:"{{count}} \u064A\u0648\u0645"},aboutXWeeks:{one:"\u062C\u0645\u0639\u0629 \u062A\u0642\u0631\u064A\u0628",two:"\u062C\u0645\u0639\u062A\u064A\u0646 \u062A\u0642\u0631\u064A\u0628",threeToTen:"{{count}} \u062C\u0645\u0627\u0639 \u062A\u0642\u0631\u064A\u0628",other:"{{count}} \u062C\u0645\u0639\u0629 \u062A\u0642\u0631\u064A\u0628"},xWeeks:{one:"\u062C\u0645\u0639\u0629",two:"\u062C\u0645\u0639\u062A\u064A\u0646",threeToTen:"{{count}} \u062C\u0645\u0627\u0639",other:"{{count}} \u062C\u0645\u0639\u0629"},aboutXMonths:{one:"\u0634\u0647\u0631 \u062A\u0642\u0631\u064A\u0628",two:"\u0634\u0647\u0631\u064A\u0646 \u062A\u0642\u0631\u064A\u0628",threeToTen:"{{count}} \u0623\u0634\u0647\u0631\u0629 \u062A\u0642\u0631\u064A\u0628",other:"{{count}} \u0634\u0647\u0631 \u062A\u0642\u0631\u064A\u0628"},xMonths:{one:"\u0634\u0647\u0631",two:"\u0634\u0647\u0631\u064A\u0646",threeToTen:"{{count}} \u0623\u0634\u0647\u0631\u0629",other:"{{count}} \u0634\u0647\u0631"},aboutXYears:{one:"\u0639\u0627\u0645 \u062A\u0642\u0631\u064A\u0628",two:"\u0639\u0627\u0645\u064A\u0646 \u062A\u0642\u0631\u064A\u0628",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645 \u062A\u0642\u0631\u064A\u0628",other:"{{count}} \u0639\u0627\u0645 \u062A\u0642\u0631\u064A\u0628"},xYears:{one:"\u0639\u0627\u0645",two:"\u0639\u0627\u0645\u064A\u0646",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645",other:"{{count}} \u0639\u0627\u0645"},overXYears:{one:"\u0623\u0643\u062B\u0631 \u0645\u0646 \u0639\u0627\u0645",two:"\u0623\u0643\u062B\u0631 \u0645\u0646 \u0639\u0627\u0645\u064A\u0646",threeToTen:"\u0623\u0643\u062B\u0631 \u0645\u0646 {{count}} \u0623\u0639\u0648\u0627\u0645",other:"\u0623\u0643\u062B\u0631 \u0645\u0646 {{count}} \u0639\u0627\u0645"},almostXYears:{one:"\u0639\u0627\u0645 \u062A\u0642\u0631\u064A\u0628",two:"\u0639\u0627\u0645\u064A\u0646 \u062A\u0642\u0631\u064A\u0628",threeToTen:"{{count}} \u0623\u0639\u0648\u0627\u0645 \u062A\u0642\u0631\u064A\u0628",other:"{{count}} \u0639\u0627\u0645 \u062A\u0642\u0631\u064A\u0628"}},N=function B(U,C,Y){var X=Z[U],J;if(typeof X==="string")J=X;else if(C===1)J=X.one;else if(C===2)J=X.two;else if(C<=10)J=X.threeToTen.replace("{{count}}",String(C));else J=X.other.replace("{{count}}",String(C));if(Y!==null&&Y!==void 0&&Y.addSuffix)if(Y.comparison&&Y.comparison>0)return"\u0641\u064A "+J;else return"\u0639\u0646\u062F\u0648 "+J;return J};function D(B){return function(){var U=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=U.width?String(U.width):B.defaultWidth,Y=B.formats[C]||B.formats[B.defaultWidth];return Y}}var $={full:"EEEE\u060C do MMMM y",long:"do MMMM y",medium:"d MMM y",short:"dd/MM/yyyy"},M={full:"HH:mm:ss",long:"HH:mm:ss",medium:"HH:mm:ss",short:"HH:mm"},S={full:"{{date}} '\u0645\u0639' {{time}}",long:"{{date}} '\u0645\u0639' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:D({formats:$,defaultWidth:"full"}),time:D({formats:M,defaultWidth:"full"}),dateTime:D({formats:S,defaultWidth:"full"})},V={lastWeek:"eeee '\u0625\u0644\u064A \u0641\u0627\u062A \u0645\u0639' p",yesterday:"'\u0627\u0644\u0628\u0627\u0631\u062D \u0645\u0639' p",today:"'\u0627\u0644\u064A\u0648\u0645 \u0645\u0639' p",tomorrow:"'\u063A\u062F\u0648\u0629 \u0645\u0639' p",nextWeek:"eeee '\u0627\u0644\u062C\u0645\u0639\u0629 \u0627\u0644\u062C\u0627\u064A\u0629 \u0645\u0639' p '\u0646\u0647\u0627\u0631'",other:"P"},L=function B(U){return V[U]};function q(B){return function(U,C){var Y=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",X;if(Y==="formatting"&&B.formattingValues){var J=B.defaultFormattingWidth||B.defaultWidth,E=C!==null&&C!==void 0&&C.width?String(C.width):J;X=B.formattingValues[E]||B.formattingValues[J]}else{var I=B.defaultWidth,x=C!==null&&C!==void 0&&C.width?String(C.width):B.defaultWidth;X=B.values[x]||B.values[I]}var O=B.argumentCallback?B.argumentCallback(U):U;return X[O]}}var f={narrow:["\u0642","\u0628"],abbreviated:["\u0642.\u0645.","\u0628.\u0645."],wide:["\u0642\u0628\u0644 \u0627\u0644\u0645\u064A\u0644\u0627\u062F","\u0628\u0639\u062F \u0627\u0644\u0645\u064A\u0644\u0627\u062F"]},j={narrow:["1","2","3","4"],abbreviated:["\u06311","\u06312","\u06313","\u06314"],wide:["\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u0623\u0648\u0644","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u062B\u0627\u0646\u064A","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u062B\u0627\u0644\u062B","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u0631\u0627\u0628\u0639"]},v={narrow:["\u062F","\u0646","\u0623","\u0633","\u0623","\u062C","\u062C","\u0645","\u0623","\u0645","\u0641","\u062C"],abbreviated:["\u062C\u0627\u0646\u0641\u064A","\u0641\u064A\u0641\u0631\u064A","\u0645\u0627\u0631\u0633","\u0623\u0641\u0631\u064A\u0644","\u0645\u0627\u064A","\u062C\u0648\u0627\u0646","\u062C\u0648\u064A\u0644\u064A\u0629","\u0623\u0648\u062A","\u0633\u0628\u062A\u0645\u0628\u0631","\u0623\u0643\u062A\u0648\u0628\u0631","\u0646\u0648\u0641\u0645\u0628\u0631","\u062F\u064A\u0633\u0645\u0628\u0631"],wide:["\u062C\u0627\u0646\u0641\u064A","\u0641\u064A\u0641\u0631\u064A","\u0645\u0627\u0631\u0633","\u0623\u0641\u0631\u064A\u0644","\u0645\u0627\u064A","\u062C\u0648\u0627\u0646","\u062C\u0648\u064A\u0644\u064A\u0629","\u0623\u0648\u062A","\u0633\u0628\u062A\u0645\u0628\u0631","\u0623\u0643\u062A\u0648\u0628\u0631","\u0646\u0648\u0641\u0645\u0628\u0631","\u062F\u064A\u0633\u0645\u0628\u0631"]},w={narrow:["\u062D","\u0646","\u062B","\u0631","\u062E","\u062C","\u0633"],short:["\u0623\u062D\u062F","\u0627\u062B\u0646\u064A\u0646","\u062B\u0644\u0627\u062B\u0627\u0621","\u0623\u0631\u0628\u0639\u0627\u0621","\u062E\u0645\u064A\u0633","\u062C\u0645\u0639\u0629","\u0633\u0628\u062A"],abbreviated:["\u0623\u062D\u062F","\u0627\u062B\u0646\u064A\u0646","\u062B\u0644\u0627\u062B\u0627\u0621","\u0623\u0631\u0628\u0639\u0627\u0621","\u062E\u0645\u064A\u0633","\u062C\u0645\u0639\u0629","\u0633\u0628\u062A"],wide:["\u0627\u0644\u0623\u062D\u062F","\u0627\u0644\u0627\u062B\u0646\u064A\u0646","\u0627\u0644\u062B\u0644\u0627\u062B\u0627\u0621","\u0627\u0644\u0623\u0631\u0628\u0639\u0627\u0621","\u0627\u0644\u062E\u0645\u064A\u0633","\u0627\u0644\u062C\u0645\u0639\u0629","\u0627\u0644\u0633\u0628\u062A"]},_={narrow:{am:"\u0635",pm:"\u0639",morning:"\u0627\u0644\u0635\u0628\u0627\u062D",noon:"\u0627\u0644\u0642\u0627\u064A\u0644\u0629",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0642\u0627\u064A\u0644\u0629",evening:"\u0627\u0644\u0639\u0634\u064A\u0629",night:"\u0627\u0644\u0644\u064A\u0644",midnight:"\u0646\u0635 \u0627\u0644\u0644\u064A\u0644"},abbreviated:{am:"\u0635",pm:"\u0639",morning:"\u0627\u0644\u0635\u0628\u0627\u062D",noon:"\u0627\u0644\u0642\u0627\u064A\u0644\u0629",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0642\u0627\u064A\u0644\u0629",evening:"\u0627\u0644\u0639\u0634\u064A\u0629",night:"\u0627\u0644\u0644\u064A\u0644",midnight:"\u0646\u0635 \u0627\u0644\u0644\u064A\u0644"},wide:{am:"\u0635",pm:"\u0639",morning:"\u0627\u0644\u0635\u0628\u0627\u062D",noon:"\u0627\u0644\u0642\u0627\u064A\u0644\u0629",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0642\u0627\u064A\u0644\u0629",evening:"\u0627\u0644\u0639\u0634\u064A\u0629",night:"\u0627\u0644\u0644\u064A\u0644",midnight:"\u0646\u0635 \u0627\u0644\u0644\u064A\u0644"}},P={narrow:{am:"\u0635",pm:"\u0639",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",noon:"\u0641\u064A \u0627\u0644\u0642\u0627\u064A\u0644\u0629",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0642\u0627\u064A\u0644\u0629",evening:"\u0641\u064A \u0627\u0644\u0639\u0634\u064A\u0629",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644",midnight:"\u0646\u0635 \u0627\u0644\u0644\u064A\u0644"},abbreviated:{am:"\u0635",pm:"\u0639",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",noon:"\u0641\u064A \u0627\u0644\u0642\u0627\u064A\u0644\u0629",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0642\u0627\u064A\u0644\u0629",evening:"\u0641\u064A \u0627\u0644\u0639\u0634\u064A\u0629",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644",midnight:"\u0646\u0635 \u0627\u0644\u0644\u064A\u0644"},wide:{am:"\u0635",pm:"\u0639",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",noon:"\u0641\u064A \u0627\u0644\u0642\u0627\u064A\u0644\u0629",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0642\u0627\u064A\u0644\u0629",evening:"\u0641\u064A \u0627\u0644\u0639\u0634\u064A\u0629",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644",midnight:"\u0646\u0635 \u0627\u0644\u0644\u064A\u0644"}},F=function B(U){return String(U)},k={ordinalNumber:F,era:q({values:f,defaultWidth:"wide"}),quarter:q({values:j,defaultWidth:"wide",argumentCallback:function B(U){return U-1}}),month:q({values:v,defaultWidth:"wide"}),day:q({values:w,defaultWidth:"wide"}),dayPeriod:q({values:_,defaultWidth:"wide",formattingValues:P,defaultFormattingWidth:"wide"})};function b(B){return function(U){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=U.match(B.matchPattern);if(!Y)return null;var X=Y[0],J=U.match(B.parsePattern);if(!J)return null;var E=B.valueCallback?B.valueCallback(J[0]):J[0];E=C.valueCallback?C.valueCallback(E):E;var I=U.slice(X.length);return{value:E,rest:I}}}function Q(B){return function(U){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},Y=C.width,X=Y&&B.matchPatterns[Y]||B.matchPatterns[B.defaultMatchWidth],J=U.match(X);if(!J)return null;var E=J[0],I=Y&&B.parsePatterns[Y]||B.parsePatterns[B.defaultParseWidth],x=Array.isArray(I)?h(I,function(z){return z.test(E)}):m(I,function(z){return z.test(E)}),O;O=B.valueCallback?B.valueCallback(x):x,O=C.valueCallback?C.valueCallback(O):O;var t=U.slice(E.length);return{value:O,rest:t}}}var m=function B(U,C){for(var Y in U)if(Object.prototype.hasOwnProperty.call(U,Y)&&C(U[Y]))return Y;return},h=function B(U,C){for(var Y=0;Y<U.length;Y++)if(C(U[Y]))return Y;return},c=/^(\d+)(th|st|nd|rd)?/i,y=/\d+/i,u={narrow:/[قب]/,abbreviated:/[قب]\.م\./,wide:/(قبل|بعد) الميلاد/},d={any:[/قبل/,/بعد/]},p={narrow:/^[1234]/i,abbreviated:/ر[1234]/,wide:/الربع (الأول|الثاني|الثالث|الرابع)/},g={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[جفمأسند]/,abbreviated:/^(جانفي|فيفري|مارس|أفريل|ماي|جوان|جويلية|أوت|سبتمبر|أكتوبر|نوفمبر|ديسمبر)/,wide:/^(جانفي|فيفري|مارس|أفريل|ماي|جوان|جويلية|أوت|سبتمبر|أكتوبر|نوفمبر|ديسمبر)/},i={narrow:[/^ج/i,/^ف/i,/^م/i,/^أ/i,/^م/i,/^ج/i,/^ج/i,/^أ/i,/^س/i,/^أ/i,/^ن/i,/^د/i],any:[/^جانفي/i,/^فيفري/i,/^مارس/i,/^أفريل/i,/^ماي/i,/^جوان/i,/^جويلية/i,/^أوت/i,/^سبتمبر/i,/^أكتوبر/i,/^نوفمبر/i,/^ديسمبر/i]},n={narrow:/^[حنثرخجس]/i,short:/^(أحد|اثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/i,abbreviated:/^(أحد|اثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/i,wide:/^(الأحد|الاثنين|الثلاثاء|الأربعاء|الخميس|الجمعة|السبت)/i},s={narrow:[/^ح/i,/^ن/i,/^ث/i,/^ر/i,/^خ/i,/^ج/i,/^س/i],wide:[/^الأحد/i,/^الاثنين/i,/^الثلاثاء/i,/^الأربعاء/i,/^الخميس/i,/^الجمعة/i,/^السبت/i],any:[/^أح/i,/^اث/i,/^ث/i,/^أر/i,/^خ/i,/^ج/i,/^س/i]},o={narrow:/^(ص|ع|ن ل|ل|(في|مع) (صباح|قايلة|عشية|ليل))/,any:/^([صع]|نص الليل|قايلة|(في|مع) (صباح|قايلة|عشية|ليل))/},r={any:{am:/^ص/,pm:/^ع/,midnight:/نص الليل/,noon:/قايلة/,afternoon:/بعد القايلة/,morning:/صباح/,evening:/عشية/,night:/ليل/}},e={ordinalNumber:b({matchPattern:c,parsePattern:y,valueCallback:function B(U){return parseInt(U,10)}}),era:Q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any"}),quarter:Q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any",valueCallback:function B(U){return U+1}}),month:Q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:Q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:Q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},a={code:"ar-TN",formatDistance:N,formatLong:R,formatRelative:L,localize:k,match:e,options:{weekStartsOn:1,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(H=window.dateFns)===null||H===void 0?void 0:H.locale),{},{arTN:a})})})();

//# debugId=06DAE0B6BBF552E364756e2164756e21
