var N=function(J){return N=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(H){return typeof H}:function(H){return H&&typeof Symbol=="function"&&H.constructor===Symbol&&H!==Symbol.prototype?"symbol":typeof H},N(J)},S=function(J,H){var X=Object.keys(J);if(Object.getOwnPropertySymbols){var Z=Object.getOwnPropertySymbols(J);H&&(Z=Z.filter(function(x){return Object.getOwnPropertyDescriptor(J,x).enumerable})),X.push.apply(X,Z)}return X},K=function(J){for(var H=1;H<arguments.length;H++){var X=arguments[H]!=null?arguments[H]:{};H%2?S(Object(X),!0).forEach(function(Z){A0(J,Z,X[Z])}):Object.getOwnPropertyDescriptors?Object.defineProperties(J,Object.getOwnPropertyDescriptors(X)):S(Object(X)).forEach(function(Z){Object.defineProperty(J,Z,Object.getOwnPropertyDescriptor(X,Z))})}return J},A0=function(J,H,X){if(H=C0(H),H in J)Object.defineProperty(J,H,{value:X,enumerable:!0,configurable:!0,writable:!0});else J[H]=X;return J},C0=function(J){var H=T0(J,"string");return N(H)=="symbol"?H:String(H)},T0=function(J,H){if(N(J)!="object"||!J)return J;var X=J[Symbol.toPrimitive];if(X!==void 0){var Z=X.call(J,H||"default");if(N(Z)!="object")return Z;throw new TypeError("@@toPrimitive must return a primitive value.")}return(H==="string"?String:Number)(J)};(function(J){var H=Object.defineProperty,X=function A(T,C){for(var B in C)H(T,B,{get:C[B],enumerable:!0,configurable:!0,set:function Y(U){return C[B]=function(){return U}}})},Z={lessThanXSeconds:{one:"\u0623\u0642\u0644 \u0645\u0646 \u062B\u0627\u0646\u064A\u0629",two:"\u0623\u0642\u0644 \u0645\u0646 \u062B\u0627\u0646\u064A\u062A\u064A\u0646",threeToTen:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062B\u0648\u0627\u0646\u064A",other:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062B\u0627\u0646\u064A\u0629"},xSeconds:{one:"\u062B\u0627\u0646\u064A\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u062B\u0627\u0646\u064A\u062A\u0627\u0646",threeToTen:"{{count}} \u062B\u0648\u0627\u0646\u064A",other:"{{count}} \u062B\u0627\u0646\u064A\u0629"},halfAMinute:"\u0646\u0635\u0641 \u062F\u0642\u064A\u0642\u0629",lessThanXMinutes:{one:"\u0623\u0642\u0644 \u0645\u0646 \u062F\u0642\u064A\u0642\u0629",two:"\u0623\u0642\u0644 \u0645\u0646 \u062F\u0642\u064A\u0642\u062A\u064A\u0646",threeToTen:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062F\u0642\u0627\u0626\u0642",other:"\u0623\u0642\u0644 \u0645\u0646 {{count}} \u062F\u0642\u064A\u0642\u0629"},xMinutes:{one:"\u062F\u0642\u064A\u0642\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u062F\u0642\u064A\u0642\u062A\u0627\u0646",threeToTen:"{{count}} \u062F\u0642\u0627\u0626\u0642",other:"{{count}} \u062F\u0642\u064A\u0642\u0629"},aboutXHours:{one:"\u0633\u0627\u0639\u0629 \u0648\u0627\u062D\u062F\u0629 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0633\u0627\u0639\u062A\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627",threeToTen:"{{count}} \u0633\u0627\u0639\u0627\u062A \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0633\u0627\u0639\u0629 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xHours:{one:"\u0633\u0627\u0639\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u0633\u0627\u0639\u062A\u0627\u0646",threeToTen:"{{count}} \u0633\u0627\u0639\u0627\u062A",other:"{{count}} \u0633\u0627\u0639\u0629"},xDays:{one:"\u064A\u0648\u0645 \u0648\u0627\u062D\u062F",two:"\u064A\u0648\u0645\u0627\u0646",threeToTen:"{{count}} \u0623\u064A\u0627\u0645",other:"{{count}} \u064A\u0648\u0645"},aboutXWeeks:{one:"\u0623\u0633\u0628\u0648\u0639 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627",two:"\u0623\u0633\u0628\u0648\u0639\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627",threeToTen:"{{count}} \u0623\u0633\u0627\u0628\u064A\u0639 \u062A\u0642\u0631\u064A\u0628\u0627",other:"{{count}} \u0623\u0633\u0628\u0648\u0639\u0627 \u062A\u0642\u0631\u064A\u0628\u0627"},xWeeks:{one:"\u0623\u0633\u0628\u0648\u0639 \u0648\u0627\u062D\u062F",two:"\u0623\u0633\u0628\u0648\u0639\u0627\u0646",threeToTen:"{{count}} \u0623\u0633\u0627\u0628\u064A\u0639",other:"{{count}} \u0623\u0633\u0628\u0648\u0639\u0627"},aboutXMonths:{one:"\u0634\u0647\u0631 \u0648\u0627\u062D\u062F \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0634\u0647\u0631\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627",threeToTen:"{{count}} \u0623\u0634\u0647\u0631 \u062A\u0642\u0631\u064A\u0628\u0627",other:"{{count}} \u0634\u0647\u0631\u0627 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xMonths:{one:"\u0634\u0647\u0631 \u0648\u0627\u062D\u062F",two:"\u0634\u0647\u0631\u0627\u0646",threeToTen:"{{count}} \u0623\u0634\u0647\u0631",other:"{{count}} \u0634\u0647\u0631\u0627"},aboutXYears:{one:"\u0633\u0646\u0629 \u0648\u0627\u062D\u062F\u0629 \u062A\u0642\u0631\u064A\u0628\u0627\u064B",two:"\u0633\u0646\u062A\u064A\u0646 \u062A\u0642\u0631\u064A\u0628\u0627",threeToTen:"{{count}} \u0633\u0646\u0648\u0627\u062A \u062A\u0642\u0631\u064A\u0628\u0627\u064B",other:"{{count}} \u0633\u0646\u0629 \u062A\u0642\u0631\u064A\u0628\u0627\u064B"},xYears:{one:"\u0633\u0646\u0629 \u0648\u0627\u062D\u062F",two:"\u0633\u0646\u062A\u0627\u0646",threeToTen:"{{count}} \u0633\u0646\u0648\u0627\u062A",other:"{{count}} \u0633\u0646\u0629"},overXYears:{one:"\u0623\u0643\u062B\u0631 \u0645\u0646 \u0633\u0646\u0629",two:"\u0623\u0643\u062B\u0631 \u0645\u0646 \u0633\u0646\u062A\u064A\u0646",threeToTen:"\u0623\u0643\u062B\u0631 \u0645\u0646 {{count}} \u0633\u0646\u0648\u0627\u062A",other:"\u0623\u0643\u062B\u0631 \u0645\u0646 {{count}} \u0633\u0646\u0629"},almostXYears:{one:"\u0645\u0627 \u064A\u0642\u0627\u0631\u0628 \u0633\u0646\u0629 \u0648\u0627\u062D\u062F\u0629",two:"\u0645\u0627 \u064A\u0642\u0627\u0631\u0628 \u0633\u0646\u062A\u064A\u0646",threeToTen:"\u0645\u0627 \u064A\u0642\u0627\u0631\u0628 {{count}} \u0633\u0646\u0648\u0627\u062A",other:"\u0645\u0627 \u064A\u0642\u0627\u0631\u0628 {{count}} \u0633\u0646\u0629"}},x=function A(T,C,B){var Y=Z[T],U;if(typeof Y==="string")U=Y;else if(C===1)U=Y.one;else if(C===2)U=Y.two;else if(C<=10)U=Y.threeToTen.replace("{{count}}",String(C));else U=Y.other.replace("{{count}}",String(C));if(B!==null&&B!==void 0&&B.addSuffix)if(B.comparison&&B.comparison>0)return"\u062E\u0644\u0627\u0644 "+U;else return"\u0645\u0646\u0630 "+U;return U};function W(A){return function(){var T=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},C=T.width?String(T.width):A.defaultWidth,B=A.formats[C]||A.formats[A.defaultWidth];return B}}var $={full:"EEEE\u060C do MMMM y",long:"do MMMM y",medium:"d MMM y",short:"dd/MM/yyyy"},D={full:"HH:mm:ss",long:"HH:mm:ss",medium:"HH:mm:ss",short:"HH:mm"},M={full:"{{date}} '\u0639\u0646\u062F \u0627\u0644\u0633\u0627\u0639\u0629' {{time}}",long:"{{date}} '\u0639\u0646\u062F \u0627\u0644\u0633\u0627\u0639\u0629' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},R={date:W({formats:$,defaultWidth:"full"}),time:W({formats:D,defaultWidth:"full"}),dateTime:W({formats:M,defaultWidth:"full"})},V={lastWeek:"eeee '\u0627\u0644\u0645\u0627\u0636\u064A \u0639\u0646\u062F \u0627\u0644\u0633\u0627\u0639\u0629' p",yesterday:"'\u0627\u0644\u0623\u0645\u0633 \u0639\u0646\u062F \u0627\u0644\u0633\u0627\u0639\u0629' p",today:"'\u0627\u0644\u064A\u0648\u0645 \u0639\u0646\u062F \u0627\u0644\u0633\u0627\u0639\u0629' p",tomorrow:"'\u063A\u062F\u0627 \u0639\u0646\u062F \u0627\u0644\u0633\u0627\u0639\u0629' p",nextWeek:"eeee '\u0627\u0644\u0642\u0627\u062F\u0645 \u0639\u0646\u062F \u0627\u0644\u0633\u0627\u0639\u0629' p",other:"P"},L=function A(T){return V[T]};function Q(A){return function(T,C){var B=C!==null&&C!==void 0&&C.context?String(C.context):"standalone",Y;if(B==="formatting"&&A.formattingValues){var U=A.defaultFormattingWidth||A.defaultWidth,I=C!==null&&C!==void 0&&C.width?String(C.width):U;Y=A.formattingValues[I]||A.formattingValues[U]}else{var E=A.defaultWidth,z=C!==null&&C!==void 0&&C.width?String(C.width):A.defaultWidth;Y=A.values[z]||A.values[E]}var O=A.argumentCallback?A.argumentCallback(T):T;return Y[O]}}var f={narrow:["\u0642","\u0628"],abbreviated:["\u0642.\u0645.","\u0628.\u0645."],wide:["\u0642\u0628\u0644 \u0627\u0644\u0645\u064A\u0644\u0627\u062F","\u0628\u0639\u062F \u0627\u0644\u0645\u064A\u0644\u0627\u062F"]},j={narrow:["1","2","3","4"],abbreviated:["\u06311","\u06312","\u06313","\u06314"],wide:["\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u0623\u0648\u0644","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u062B\u0627\u0646\u064A","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u062B\u0627\u0644\u062B","\u0627\u0644\u0631\u0628\u0639 \u0627\u0644\u0631\u0627\u0628\u0639"]},v={narrow:["\u064A","\u0641","\u0645","\u0623","\u0645","\u064A","\u064A","\u0623","\u0633","\u0623","\u0646","\u062F"],abbreviated:["\u064A\u0646\u0627\u064A\u0631","\u0641\u0628\u0631\u0627\u064A\u0631","\u0645\u0627\u0631\u0633","\u0623\u0628\u0631\u064A\u0644","\u0645\u0627\u064A\u0648","\u064A\u0648\u0646\u064A\u0648","\u064A\u0648\u0644\u064A\u0648","\u0623\u063A\u0633\u0637\u0633","\u0633\u0628\u062A\u0645\u0628\u0631","\u0623\u0643\u062A\u0648\u0628\u0631","\u0646\u0648\u0641\u0645\u0628\u0631","\u062F\u064A\u0633\u0645\u0628\u0631"],wide:["\u064A\u0646\u0627\u064A\u0631","\u0641\u0628\u0631\u0627\u064A\u0631","\u0645\u0627\u0631\u0633","\u0623\u0628\u0631\u064A\u0644","\u0645\u0627\u064A\u0648","\u064A\u0648\u0646\u064A\u0648","\u064A\u0648\u0644\u064A\u0648","\u0623\u063A\u0633\u0637\u0633","\u0633\u0628\u062A\u0645\u0628\u0631","\u0623\u0643\u062A\u0648\u0628\u0631","\u0646\u0648\u0641\u0645\u0628\u0631","\u062F\u064A\u0633\u0645\u0628\u0631"]},P={narrow:["\u062D","\u0646","\u062B","\u0631","\u062E","\u062C","\u0633"],short:["\u0623\u062D\u062F","\u0627\u062B\u0646\u064A\u0646","\u062B\u0644\u0627\u062B\u0627\u0621","\u0623\u0631\u0628\u0639\u0627\u0621","\u062E\u0645\u064A\u0633","\u062C\u0645\u0639\u0629","\u0633\u0628\u062A"],abbreviated:["\u0623\u062D\u062F","\u0627\u062B\u0646\u064A\u0646","\u062B\u0644\u0627\u062B\u0627\u0621","\u0623\u0631\u0628\u0639\u0627\u0621","\u062E\u0645\u064A\u0633","\u062C\u0645\u0639\u0629","\u0633\u0628\u062A"],wide:["\u0627\u0644\u0623\u062D\u062F","\u0627\u0644\u0627\u062B\u0646\u064A\u0646","\u0627\u0644\u062B\u0644\u0627\u062B\u0627\u0621","\u0627\u0644\u0623\u0631\u0628\u0639\u0627\u0621","\u0627\u0644\u062E\u0645\u064A\u0633","\u0627\u0644\u062C\u0645\u0639\u0629","\u0627\u0644\u0633\u0628\u062A"]},_={narrow:{am:"\u0635",pm:"\u0645",morning:"\u0627\u0644\u0635\u0628\u0627\u062D",noon:"\u0627\u0644\u0638\u0647\u0631",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0627\u0644\u0644\u064A\u0644",midnight:"\u0645\u0646\u062A\u0635\u0641 \u0627\u0644\u0644\u064A\u0644"},abbreviated:{am:"\u0635",pm:"\u0645",morning:"\u0627\u0644\u0635\u0628\u0627\u062D",noon:"\u0627\u0644\u0638\u0647\u0631",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0627\u0644\u0644\u064A\u0644",midnight:"\u0645\u0646\u062A\u0635\u0641 \u0627\u0644\u0644\u064A\u0644"},wide:{am:"\u0635",pm:"\u0645",morning:"\u0627\u0644\u0635\u0628\u0627\u062D",noon:"\u0627\u0644\u0638\u0647\u0631",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0627\u0644\u0644\u064A\u0644",midnight:"\u0645\u0646\u062A\u0635\u0641 \u0627\u0644\u0644\u064A\u0644"}},w={narrow:{am:"\u0635",pm:"\u0645",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",noon:"\u0627\u0644\u0638\u0647\u0631",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644",midnight:"\u0645\u0646\u062A\u0635\u0641 \u0627\u0644\u0644\u064A\u0644"},abbreviated:{am:"\u0635",pm:"\u0645",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",noon:"\u0627\u0644\u0638\u0647\u0631",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644",midnight:"\u0645\u0646\u062A\u0635\u0641 \u0627\u0644\u0644\u064A\u0644"},wide:{am:"\u0635",pm:"\u0645",morning:"\u0641\u064A \u0627\u0644\u0635\u0628\u0627\u062D",noon:"\u0627\u0644\u0638\u0647\u0631",afternoon:"\u0628\u0639\u062F \u0627\u0644\u0638\u0647\u0631",evening:"\u0641\u064A \u0627\u0644\u0645\u0633\u0627\u0621",night:"\u0641\u064A \u0627\u0644\u0644\u064A\u0644",midnight:"\u0645\u0646\u062A\u0635\u0641 \u0627\u0644\u0644\u064A\u0644"}},F=function A(T){return String(T)},k={ordinalNumber:F,era:Q({values:f,defaultWidth:"wide"}),quarter:Q({values:j,defaultWidth:"wide",argumentCallback:function A(T){return T-1}}),month:Q({values:v,defaultWidth:"wide"}),day:Q({values:P,defaultWidth:"wide"}),dayPeriod:Q({values:_,defaultWidth:"wide",formattingValues:w,defaultFormattingWidth:"wide"})};function b(A){return function(T){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=T.match(A.matchPattern);if(!B)return null;var Y=B[0],U=T.match(A.parsePattern);if(!U)return null;var I=A.valueCallback?A.valueCallback(U[0]):U[0];I=C.valueCallback?C.valueCallback(I):I;var E=T.slice(Y.length);return{value:I,rest:E}}}function q(A){return function(T){var C=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},B=C.width,Y=B&&A.matchPatterns[B]||A.matchPatterns[A.defaultMatchWidth],U=T.match(Y);if(!U)return null;var I=U[0],E=B&&A.parsePatterns[B]||A.parsePatterns[A.defaultParseWidth],z=Array.isArray(E)?h(E,function(G){return G.test(I)}):m(E,function(G){return G.test(I)}),O;O=A.valueCallback?A.valueCallback(z):z,O=C.valueCallback?C.valueCallback(O):O;var t=T.slice(I.length);return{value:O,rest:t}}}var m=function A(T,C){for(var B in T)if(Object.prototype.hasOwnProperty.call(T,B)&&C(T[B]))return B;return},h=function A(T,C){for(var B=0;B<T.length;B++)if(C(T[B]))return B;return},y=/^(\d+)(th|st|nd|rd)?/i,c=/\d+/i,u={narrow:/[قب]/,abbreviated:/[قب]\.م\./,wide:/(قبل|بعد) الميلاد/},d={any:[/قبل/,/بعد/]},p={narrow:/^[1234]/i,abbreviated:/ر[1234]/,wide:/الربع (الأول|الثاني|الثالث|الرابع)/},g={any:[/1/i,/2/i,/3/i,/4/i]},l={narrow:/^[أيفمسند]/,abbreviated:/^(يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر)/,wide:/^(يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر)/},i={narrow:[/^ي/i,/^ف/i,/^م/i,/^أ/i,/^م/i,/^ي/i,/^ي/i,/^أ/i,/^س/i,/^أ/i,/^ن/i,/^د/i],any:[/^يناير/i,/^فبراير/i,/^مارس/i,/^أبريل/i,/^مايو/i,/^يونيو/i,/^يوليو/i,/^أغسطس/i,/^سبتمبر/i,/^أكتوبر/i,/^نوفمبر/i,/^ديسمبر/i]},n={narrow:/^[حنثرخجس]/i,short:/^(أحد|اثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/i,abbreviated:/^(أحد|اثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/i,wide:/^(الأحد|الاثنين|الثلاثاء|الأربعاء|الخميس|الجمعة|السبت)/i},s={narrow:[/^ح/i,/^ن/i,/^ث/i,/^ر/i,/^خ/i,/^ج/i,/^س/i],wide:[/^الأحد/i,/^الاثنين/i,/^الثلاثاء/i,/^الأربعاء/i,/^الخميس/i,/^الجمعة/i,/^السبت/i],any:[/^أح/i,/^اث/i,/^ث/i,/^أر/i,/^خ/i,/^ج/i,/^س/i]},o={narrow:/^(ص|م|منتصف الليل|الظهر|بعد الظهر|في الصباح|في المساء|في الليل)/,any:/^(ص|م|منتصف الليل|الظهر|بعد الظهر|في الصباح|في المساء|في الليل)/},r={any:{am:/^ص/,pm:/^م/,midnight:/منتصف الليل/,noon:/الظهر/,afternoon:/بعد الظهر/,morning:/في الصباح/,evening:/في المساء/,night:/في الليل/}},a={ordinalNumber:b({matchPattern:y,parsePattern:c,valueCallback:function A(T){return parseInt(T,10)}}),era:q({matchPatterns:u,defaultMatchWidth:"wide",parsePatterns:d,defaultParseWidth:"any"}),quarter:q({matchPatterns:p,defaultMatchWidth:"wide",parsePatterns:g,defaultParseWidth:"any",valueCallback:function A(T){return T+1}}),month:q({matchPatterns:l,defaultMatchWidth:"wide",parsePatterns:i,defaultParseWidth:"any"}),day:q({matchPatterns:n,defaultMatchWidth:"wide",parsePatterns:s,defaultParseWidth:"any"}),dayPeriod:q({matchPatterns:o,defaultMatchWidth:"any",parsePatterns:r,defaultParseWidth:"any"})},e={code:"ar",formatDistance:x,formatLong:R,formatRelative:L,localize:k,match:a,options:{weekStartsOn:6,firstWeekContainsDate:1}};window.dateFns=K(K({},window.dateFns),{},{locale:K(K({},(J=window.dateFns)===null||J===void 0?void 0:J.locale),{},{ar:e})})})();

//# debugId=C0D95CC4C0C7AB4F64756e2164756e21
